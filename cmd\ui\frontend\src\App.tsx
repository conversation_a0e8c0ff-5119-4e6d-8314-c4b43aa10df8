// src/App.tsx (React)
import { useEffect, useState } from "react";
import { EventsOn } from "../wailsjs/runtime/runtime";
import { PushStatus } from "../wailsjs/go/uiapp/App"; // auto-gen khi bind App

export default function App() {
  const [status, setStatus] = useState("Starting...");

  useEffect(() => {
    const off = EventsOn("status", (s: string) => setStatus(s));
    return () => off();
  }, []);

  return (
    <div style={{ padding: 16 }}>
      <h3>SystemSupport</h3>
      <div>Status: {status}</div>
      <button onClick={() => PushStatus("Manual update from UI")}>
        Test Push
      </button>
    </div>
  );
}
